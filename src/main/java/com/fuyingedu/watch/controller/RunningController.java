package com.fuyingedu.watch.controller;

import com.fuyingedu.watch.comm.interceptor.Login;
import com.fuyingedu.watch.model.CommResp;
import com.fuyingedu.watch.model.running.RecordResp;
import com.fuyingedu.watch.model.running.ReportResp;
import com.fuyingedu.watch.model.running.StatResp;
import com.fuyingedu.watch.service.RunningService;
import com.fuyingedu.watch.model.running.SaveReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

import java.util.List;

/**
 * 跑步相关接口
 */
@RestController
@RequestMapping("/api/watch/running")
public class RunningController {

    @Autowired
    private RunningService runningService;

    /**
     * 保存跑步记录和轨迹
     * @param uid      用户ID
     * @param req      跑步记录和轨迹信息
     * @return 保存结果
     */
    @PostMapping("/save")
    @Login
    public CommResp<?> saveRunningRecord(
            @Login Long uid,
            @RequestBody @Valid SaveReq req
    ) {
        return runningService.saveRunningRecord(uid, req);
    }

    /**
     * 统计记录查询
     * @param type 1-周 2-月 3-年
     */
    @GetMapping("/stat")
    @Login
    public CommResp<List<StatResp>> statRunningRecords(
            @Login Long uid,
            @RequestParam Long accountId,
            @RequestParam Integer type
    ) {
        return runningService.statRunningRecords(uid, accountId, type);
    }

    /**
     * 跑步记录查询
     * @param type 1-日 2-周 3-月
     */
    @GetMapping("/query")
    @Login
    public CommResp<List<RecordResp>> queryRunningRecords(
            @Login Long uid,
            @RequestParam Long accountId,
            @RequestParam Integer type,
            @RequestParam(required = false) Long objId
    ) {
        return runningService.queryRunningRecords(uid, accountId, type, objId);
    }

    /**
     * 跑步报表查询
     * @param type 2-周 3-月 4-年
     */
    @GetMapping("/report")
    @Login
    public CommResp<ReportResp> reportRunningRecords(
            @Login Long uid,
            @RequestParam Long accountId,
            @RequestParam Integer type,
            @RequestParam Long objId
    ) {
        return runningService.reportRunningRecords(uid, accountId, type, objId);
    }
}