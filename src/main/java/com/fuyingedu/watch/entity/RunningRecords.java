package com.fuyingedu.watch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 用户跑步记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("watch_running_records")
public class RunningRecords {

    /**
     * 主键，跑步记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID，关联用户表
     */
    private Long accountId;

    /**
     * 跑步开始时间
     */
    private LocalDateTime startTime;

    /**
     * 跑步截止时间
     */
    private LocalDateTime endTime;

    /**
     * 跑步距离（单位：米）
     */
    private Integer distance;

    /**
     * 动态消耗卡路里
     */
    private Integer dynamicCalories;

    /**
     * 总消耗卡路里
     */
    private Integer totalCalories;

    /**
     * 平均功率（单位：瓦）
     */
    private Integer avgPower;

    /**
     * 平均步频（步/分钟）
     */
    private Short avgCadence;

    /**
     * 平均心率（bpm）
     */
    private Short avgHeartRate;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;
}
