package com.fuyingedu.watch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 每年跑步统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("watch_annual_running_stats")
public class AnnualRunningStats {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账号ID，关联用户表
     */
    private Long accountId;

    /**
     * 年份，如：2024
     */
    private String yearStr;

    /**
     * 本年跑步次数
     */
    private Integer runCount;

    /**
     * 本年总跑步时间（秒）
     */
    private Integer totalDuration;

    /**
     * 本年总跑步距离（米）
     */
    private Integer totalDistance;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 记录更新时间
     */
    private LocalDateTime updatedAt;
}
