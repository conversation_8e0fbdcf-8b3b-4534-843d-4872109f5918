package com.fuyingedu.watch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 每周跑步统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("watch_weekly_running_stats")
public class WeeklyRunningStats {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 账号ID，关联用户表
     */
    private Long accountId;

    /**
     * 年-周，格式：2024-01，表示2024年第1周
     */
    private String yearWeek;

    /**
     * 本周跑步次数
     */
    private Integer runCount;

    /**
     * 本周总跑步时间（秒）
     */
    private Integer totalDuration;

    /**
     * 本周总跑步距离（米）
     */
    private Integer totalDistance;

    /**
     * 记录创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 记录更新时间
     */
    private LocalDateTime updatedAt;
}
