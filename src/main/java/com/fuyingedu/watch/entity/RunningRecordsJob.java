package com.fuyingedu.watch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * <p>
 * 用于统计时减少watch_daily_steps表的数据扫描
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-12
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("watch_running_records_job")
public class RunningRecordsJob {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 年月/周格式：NYYYY-MM N-1月2-日
     */
    private String yearStr;

    /**
     * 本月统计的watch_daily_steps表的最大值
     */
    private Long maxId;
}
