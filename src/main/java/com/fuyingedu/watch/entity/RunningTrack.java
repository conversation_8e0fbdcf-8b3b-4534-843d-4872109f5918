package com.fuyingedu.watch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 跑步轨迹表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-11
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
@TableName("watch_running_track")
public class RunningTrack {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 跑步记录id
     */
    private Long recordId;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 数据时间
     */
    private LocalDateTime pointTime;
}
