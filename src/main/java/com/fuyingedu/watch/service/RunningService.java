package com.fuyingedu.watch.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fuyingedu.watch.comm.util.DateUtils;
import com.fuyingedu.watch.entity.*;
import com.fuyingedu.watch.mapper.*;
import com.fuyingedu.watch.model.CommResp;
import com.fuyingedu.watch.model.running.*;
import com.fuyingedu.watch.model.running.TrackResp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class RunningService {

    @Autowired
    private RunningRecordsMapper runningRecordsMapper;
    @Autowired
    private RunningTrackMapper runningTrackMapper;
    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private WeeklyRunningStatsMapper weeklyRunningStatsMapper;
    @Autowired
    private MonthlyRunningStatsMapper monthlyRunningStatsMapper;
    @Autowired
    private AnnualRunningStatsMapper annualRunningStatsMapper;
    @Autowired
    private RunningRecordsJobMapper runningRecordsJobMapper;

    /**
     * 验证账号是否属于当前用户
     */
    private CommResp<Account> validateAccount(Long uid, Long accountId) {
        LambdaQueryWrapper<Account> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.select(Account::getId)
                .eq(Account::getUid, uid)
                .eq(Account::getId, accountId);
        Account account = accountMapper.selectOne(accountQueryWrapper);

        if (account == null) {
            return CommResp.error("用户账号不存在或无权限操作");
        }
        return CommResp.success(account);
    }

    /**
     * 计算配速
     * @param totalDuration 总时间(秒)
     * @param totalDistance 总距离(米)
     * @return 配速字符串(分钟/公里)
     */
    private String calculatePace(Integer totalDuration, Integer totalDistance) {
        if (totalDistance != null && totalDistance > 0 && 
            totalDuration != null && totalDuration > 0) {
            // 配速 = 总时间(分钟) / 总距离(公里)
            double pace = (totalDuration / 60.0) / (totalDistance / 1000.0);
            return String.format("%.2f", pace);
        }
        return "0.00";
    }

    /**
     * 计算跑步记录的统计数据
     * @param records 跑步记录列表
     * @return 包含跑步次数、总时间和总距离的数组
     */
    private int[] calculateRunningStats(List<RunningRecords> records) {
        int runCount = records.size();
        int totalDuration = records.stream()
                .mapToInt(record -> {
                    long duration = Duration.between(record.getStartTime(), record.getEndTime()).getSeconds();
                    return (int) duration;
                })
                .sum();
        int totalDistance = records.stream()
                .mapToInt(RunningRecords::getDistance)
                .sum();
        
        return new int[]{runCount, totalDuration, totalDistance};
    }

    /**
     * 保存跑步记录和轨迹
     */
    @Transactional(rollbackFor = Exception.class)
    public CommResp<?> saveRunningRecord(Long uid, SaveReq req) {
        // 验证账号是否属于当前用户
        CommResp<Account> accountCheck = validateAccount(uid, req.getAccountId());
        if (accountCheck.isFail()) {
            return accountCheck;
        }
        // 转换DTO到实体类
        RunningRecords runningRecord = RunningConvertor.toRunningRecords(req);

        // 保存跑步记录
        runningRecordsMapper.insert(runningRecord);

        // 获取生成的记录ID
        Long recordId = runningRecord.getId();

        // 转换轨迹DTO到实体类
        List<RunningTrack> trackList = RunningConvertor.toRunningTrackList(req.getTrackList());

        // 保存轨迹点
        for (RunningTrack track : trackList) {
            track.setRecordId(recordId);
            runningTrackMapper.insert(track);
        }
        return CommResp.success();
    }

    /**
     * 统计跑步记录
     * 1. 周：从 watch_weekly_running_stats 表中查询最近52周的数据
     * 2. 月：从 watch_monthly_running_stats 表中查询最近12个月的数据
     * 3. 年：从 watch_annual_running_stats 表中查询最近5年的数据
     * @param type  1-周 2-月 3-年
     */
    public CommResp<List<StatResp>> statRunningRecords(Long uid, Long accountId, Integer type) {
        // 验证账号是否属于当前用户
        CommResp<Account> accountCheck = validateAccount(uid, accountId);
        if (accountCheck.isFail()) {
            return CommResp.error("用户账号不存在或无权限操作");
        }

        List<StatResp> statResps;
        
        switch (type) {
            case 1: // 周统计
                // 查询最近52周的数据
                LambdaQueryWrapper<WeeklyRunningStats> weekQuery = new LambdaQueryWrapper<>();
                weekQuery.eq(WeeklyRunningStats::getAccountId, accountId)
                        .orderByDesc(WeeklyRunningStats::getYearWeek)
                        .last("LIMIT 52");
                List<WeeklyRunningStats> weeklyStats = weeklyRunningStatsMapper.selectList(weekQuery);
                statResps = weeklyStats.stream()
                        .map(this::convertToStatResp)
                        .collect(Collectors.toList());
                break;
            case 2: // 月统计
                // 查询最近12个月的数据
                LambdaQueryWrapper<MonthlyRunningStats> monthQuery = new LambdaQueryWrapper<>();
                monthQuery.eq(MonthlyRunningStats::getAccountId, accountId)
                        .orderByDesc(MonthlyRunningStats::getYearMonthStr)
                        .last("LIMIT 12");
                List<MonthlyRunningStats> monthlyStats = monthlyRunningStatsMapper.selectList(monthQuery);
                statResps = monthlyStats.stream()
                        .map(this::convertToStatResp)
                        .collect(Collectors.toList());
                break;
            case 3: // 年统计
                // 查询最近5年的数据
                LambdaQueryWrapper<AnnualRunningStats> yearQuery = new LambdaQueryWrapper<>();
                yearQuery.eq(AnnualRunningStats::getAccountId, accountId)
                        .orderByDesc(AnnualRunningStats::getYearStr)
                        .last("LIMIT 5");
                List<AnnualRunningStats> annualStats = annualRunningStatsMapper.selectList(yearQuery);
                statResps = annualStats.stream()
                        .map(this::convertToStatResp)
                        .collect(Collectors.toList());
                break;
            default:
                return CommResp.error("不支持的统计类型");
        }

        return CommResp.success(statResps);
    }

    /**
     * 将WeeklyRunningStats转换为StatResp
     */
    private StatResp convertToStatResp(WeeklyRunningStats weeklyStats) {
        StatResp statResp = new StatResp();
        statResp.setObjId(weeklyStats.getId());
        statResp.setRunCount(weeklyStats.getRunCount());
        statResp.setTotalDuration(weeklyStats.getTotalDuration());
        statResp.setTotalDistance(weeklyStats.getTotalDistance());
        
        // 设置开始时间和结束时间
        String yearWeek = weeklyStats.getYearWeek();
        LocalDate weekStart = DateUtils.getWeekStart(yearWeek);
        LocalDate weekEnd = weekStart.plusDays(6);
        statResp.setStartTime(DateUtils.format(weekStart, DateUtils.DATE_FORMATTER));
        statResp.setEndTime(DateUtils.format(weekEnd, DateUtils.DATE_FORMATTER));
        
        // 设置统计时间描述
        statResp.setStatTime(yearWeek + "周");
        
        // 计算平均配速
        statResp.setPace(RunningConvertor.getPace(weeklyStats.getTotalDuration(), weeklyStats.getTotalDistance()));

        return statResp;
    }

    /**
     * 将MonthlyRunningStats转换为StatResp
     */
    private StatResp convertToStatResp(MonthlyRunningStats monthlyStats) {
        StatResp statResp = new StatResp();
        statResp.setObjId(monthlyStats.getId());
        statResp.setRunCount(monthlyStats.getRunCount());
        statResp.setTotalDuration(monthlyStats.getTotalDuration());
        statResp.setTotalDistance(monthlyStats.getTotalDistance());
        
        // 设置开始时间和结束时间
        String yearMonth = monthlyStats.getYearMonthStr();
        LocalDate monthStart = DateUtils.getMonthStart(yearMonth);
        LocalDate monthEnd = monthStart.plusMonths(1).minusDays(1);
        statResp.setStartTime(DateUtils.format(monthStart, DateUtils.DATE_FORMATTER));
        statResp.setEndTime(DateUtils.format(monthEnd, DateUtils.DATE_FORMATTER));
        
        // 设置统计时间描述
        statResp.setStatTime(yearMonth);
        
        // 计算平均配速
        statResp.setPace(RunningConvertor.getPace(monthlyStats.getTotalDuration(), monthlyStats.getTotalDistance()));
        
        return statResp;
    }

    /**
     * 将AnnualRunningStats转换为StatResp
     */
    private StatResp convertToStatResp(AnnualRunningStats annualStats) {
        StatResp statResp = new StatResp();
        statResp.setObjId(annualStats.getId());
        statResp.setRunCount(annualStats.getRunCount());
        statResp.setTotalDuration(annualStats.getTotalDuration());
        statResp.setTotalDistance(annualStats.getTotalDistance());
        
        // 设置开始时间和结束时间
        String yearStr = annualStats.getYearStr();
        LocalDate yearStart = LocalDate.parse(yearStr + "-01-01");
        LocalDate yearEnd = LocalDate.parse(yearStr + "-12-31");
        statResp.setStartTime(DateUtils.format(yearStart, DateUtils.DATE_FORMATTER));
        statResp.setEndTime(DateUtils.format(yearEnd, DateUtils.DATE_FORMATTER));
        
        // 设置统计时间描述
        statResp.setStatTime(yearStr + "年");
        
        // 计算平均配速
        statResp.setPace(calculatePace(annualStats.getTotalDuration(), annualStats.getTotalDistance()));
        
        return statResp;
    }

    /**
     * 查询跑步记录
     * 1. 日：直接从 watch_running_records 表中查询最近200次的跑步记录
     * 2. 周：需要从根据objId从 watch_weekly_running_stats 表中查出对应的是哪一年的哪一周，并计算出开始时间和结束时间
     *  再根据开始时间和结束时间从 watch_running_records 表中查询出数据
     * 3. 月：需要从根据objId从 watch_monthly_running_stats 表中查出对应的是哪一年的哪一月，并计算出开始时间和结束时间
     *  再根据开始时间和结束时间从 watch_running_records 表中查询出数据
     * @param type 1-日 2-周 3-月
     */
    public CommResp<List<RecordResp>> queryRunningRecords(Long uid, Long accountId, Integer type, Long objId) {
        // 验证账号是否属于当前用户
        CommResp<Account> accountCheck = validateAccount(uid, accountId);
        if (accountCheck.isFail()) {
            return CommResp.error("用户账号不存在或无权限操作");
        }

        List<RecordResp> recordResps = new ArrayList<>();

        LambdaQueryWrapper<RunningRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(RunningRecords::getId, RunningRecords::getAccountId,
                RunningRecords::getStartTime, RunningRecords::getEndTime, RunningRecords::getDistance);
        queryWrapper.eq(RunningRecords::getAccountId, accountId);
        switch (type) {
            case 1: // 日
                // 查询最近200次跑步记录
                queryWrapper.orderByDesc(RunningRecords::getId).last("LIMIT 200");
                List<RunningRecords> dailyRecords = runningRecordsMapper.selectList(queryWrapper);
                recordResps = dailyRecords.stream()
                        .map(RunningConvertor::toRecordResp)
                        .collect(Collectors.toList());
                break;
            case 2: // 周
                // 根据objId从 watch_weekly_running_stats 表中查出对应的是哪一年的哪一周
                WeeklyRunningStats weeklyStats = weeklyRunningStatsMapper.selectById(objId);
                if (weeklyStats != null) {
                    String yearWeek = weeklyStats.getYearWeek();
                    // 计算开始时间和结束时间
                    LocalDate weekStart = DateUtils.getWeekStart(yearWeek);
                    LocalDateTime weekStartTime = weekStart.atStartOfDay();
                    LocalDateTime weekEndTime = weekStart.plusDays(7).atStartOfDay();
                    // 根据开始时间和结束时间从 watch_running_records 表中查询出数据
                    queryWrapper.ge(RunningRecords::getStartTime, weekStartTime)
                            .lt(RunningRecords::getStartTime, weekEndTime)
                            .orderByDesc(RunningRecords::getStartTime);
                    List<RunningRecords> weeklyRecords = runningRecordsMapper.selectList(queryWrapper);
                    recordResps = weeklyRecords.stream()
                            .map(RunningConvertor::toRecordResp)
                            .collect(Collectors.toList());
                }
                break;
            case 3: // 月
                // 根据objId从 watch_monthly_running_stats 表中查出对应的是哪一年的哪一月
                MonthlyRunningStats monthlyStats = monthlyRunningStatsMapper.selectById(objId);
                if (monthlyStats != null) {
                    String yearMonth = monthlyStats.getYearMonthStr();
                    // 计算开始时间和结束时间
                    LocalDate monthStart = DateUtils.getMonthStart(yearMonth);
                    LocalDateTime monthStartTime = monthStart.atStartOfDay();
                    LocalDateTime monthEndTime = monthStart.plusMonths(1).atStartOfDay();
                    // 根据开始时间和结束时间从 watch_running_records 表中查询出数据
                    queryWrapper.ge(RunningRecords::getStartTime, monthStartTime)
                            .lt(RunningRecords::getStartTime, monthEndTime)
                            .orderByDesc(RunningRecords::getStartTime);
                    List<RunningRecords> monthlyRecords = runningRecordsMapper.selectList(queryWrapper);
                    recordResps = monthlyRecords.stream()
                            .map(RunningConvertor::toRecordResp)
                            .collect(Collectors.toList());
                }
                break;
            default:
                return CommResp.error("不支持的查询类型");
        }

        return CommResp.success(recordResps);
    }

    /**
     * 查询跑步记录报表
     * 2. 周：根据objId从 watch_weekly_running_stats 表中查出本周的统计数据和对应的是哪一年的哪一周，并计算出开始时间和结束时间
     *  再根据开始时间和结束时间从 watch_running_records 表中查询出每天的数据放到trackList中，需要补全7天
     * 3. 月：根据objId从 watch_monthly_running_stats 表中查出本月的数据和对应的是哪一年的哪一月，并计算出开始时间和结束时间
     *  再根据开始时间和结束时间从 watch_running_records 表中查询出每天的数据放到trackList中，需要补全一个月的每一天
     * 4. 年：根据objId从 watch_annual_running_stats 表中查出对应的年份，并计算出开始时间和结束时间
     *  再根据开始时间和结束时间从 watch_monthly_running_stats 表中查询出每月的数据放到trackList中，需要补全12个月
     */
    public CommResp<ReportResp> reportRunningRecords(Long uid, Long accountId, Integer type, Long objId) {
        // 验证账号是否属于当前用户
        CommResp<Account> accountCheck = validateAccount(uid, accountId);
        if (accountCheck.isFail()) {
            return CommResp.error("用户账号不存在或无权限操作");
        }

        ReportResp reportResp = new ReportResp();

        return switch (type) {
            case 2 -> // 周报表
                    CommResp.success(buildWeeklyReport(accountId, objId, reportResp));
            case 3 -> // 月报表
                    CommResp.success(buildMonthlyReport(accountId, objId, reportResp));
            case 4 -> // 年报表
                    CommResp.success(buildAnnualReport(accountId, objId, reportResp));
            default -> CommResp.error("不支持的报表类型");
        };
    }

    /**
     * 构建周报表
     */
    private ReportResp buildWeeklyReport(Long accountId, Long objId, ReportResp reportResp) {
        // 根据objId从 watch_weekly_running_stats 表中查出本周的统计数据
        WeeklyRunningStats weeklyStats = weeklyRunningStatsMapper.selectById(objId);
        if (weeklyStats == null) {
            return reportResp;
        }

        // 设置基本统计信息
        reportResp.setRunCount(weeklyStats.getRunCount());
        reportResp.setDuration(weeklyStats.getTotalDuration());
        reportResp.setDistance(weeklyStats.getTotalDistance());
        
        // 计算平均配速
        reportResp.setPace(calculatePace(weeklyStats.getTotalDuration(), weeklyStats.getTotalDistance()));

        // 计算开始时间和结束时间
        String yearWeek = weeklyStats.getYearWeek();
        LocalDate weekStart = DateUtils.getWeekStart(yearWeek);
        LocalDateTime weekStartTime = weekStart.atStartOfDay();
        LocalDateTime weekEndTime = weekStart.plusDays(7).atStartOfDay();

        // 一次性查询一周的所有跑步记录
        LambdaQueryWrapper<RunningRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RunningRecords::getAccountId, accountId)
                .ge(RunningRecords::getStartTime, weekStartTime)
                .lt(RunningRecords::getStartTime, weekEndTime);
        
        List<RunningRecords> allWeeklyRecords = runningRecordsMapper.selectList(queryWrapper);
        
        // 按日期分组记录
        Map<LocalDate, List<RunningRecords>> recordsByDate = allWeeklyRecords.stream()
                .collect(Collectors.groupingBy(record -> record.getStartTime().toLocalDate()));

        // 查询每天的跑步记录
        List<TrackResp> dailyReports = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            LocalDate currentDate = weekStart.plusDays(i);
            List<RunningRecords> dailyRecords = recordsByDate.getOrDefault(currentDate, Collections.emptyList());
            
            // 统计当天数据
            TrackResp dailyReport = new TrackResp();
            if (!dailyRecords.isEmpty()) {
                int runCount = dailyRecords.size();
                int totalDuration = dailyRecords.stream()
                        .mapToInt(record -> {
                            long duration = Duration.between(record.getStartTime(), record.getEndTime()).getSeconds();
                            return (int) duration;
                        })
                        .sum();
                int totalDistance = dailyRecords.stream()
                        .mapToInt(RunningRecords::getDistance)
                        .sum();
                
                dailyReport.setRunCount(runCount);
                dailyReport.setDuration(totalDuration);
                dailyReport.setDistance(totalDistance);
                
                // 计算当天平均配速
                dailyReport.setPace(calculatePace(totalDuration, totalDistance));
            }
            
            dailyReports.add(dailyReport);
        }
        
        reportResp.setTrackList(dailyReports);
        return reportResp;
    }

    /**
     * 构建月报表
     */
    private ReportResp buildMonthlyReport(Long accountId, Long objId, ReportResp reportResp) {
        // 根据objId从 watch_monthly_running_stats 表中查出本月的统计数据
        MonthlyRunningStats monthlyStats = monthlyRunningStatsMapper.selectById(objId);
        if (monthlyStats == null) {
            return reportResp;
        }

        // 设置基本统计信息
        reportResp.setRunCount(monthlyStats.getRunCount());
        reportResp.setDuration(monthlyStats.getTotalDuration());
        reportResp.setDistance(monthlyStats.getTotalDistance());
        
        // 计算平均配速
        reportResp.setPace(calculatePace(monthlyStats.getTotalDuration(), monthlyStats.getTotalDistance()));

        // 计算开始时间和结束时间
        String yearMonth = monthlyStats.getYearMonthStr();
        LocalDate monthStart = DateUtils.getMonthStart(yearMonth);
        int daysInMonth = monthStart.lengthOfMonth();
        LocalDateTime monthStartTime = monthStart.atStartOfDay();
        LocalDateTime monthEndTime = monthStart.plusMonths(1).atStartOfDay();

        // 一次性查询一个月的所有跑步记录
        LambdaQueryWrapper<RunningRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RunningRecords::getAccountId, accountId)
                .ge(RunningRecords::getStartTime, monthStartTime)
                .lt(RunningRecords::getStartTime, monthEndTime);
        
        List<RunningRecords> allMonthlyRecords = runningRecordsMapper.selectList(queryWrapper);
        
        // 按日期分组记录
        Map<LocalDate, List<RunningRecords>> recordsByDate = allMonthlyRecords.stream()
                .collect(Collectors.groupingBy(record -> record.getStartTime().toLocalDate()));

        // 查询每天的跑步记录
        List<TrackResp> dailyReports = new ArrayList<>();
        for (int i = 0; i < daysInMonth; i++) {
            LocalDate currentDate = monthStart.plusDays(i);
            List<RunningRecords> dailyRecords = recordsByDate.getOrDefault(currentDate, Collections.emptyList());
            
            // 统计当天数据
            TrackResp dailyReport = new TrackResp();
            if (!dailyRecords.isEmpty()) {
                int runCount = dailyRecords.size();
                int totalDuration = dailyRecords.stream()
                        .mapToInt(record -> {
                            long duration = Duration.between(record.getStartTime(), record.getEndTime()).getSeconds();
                            return (int) duration;
                        })
                        .sum();
                int totalDistance = dailyRecords.stream()
                        .mapToInt(RunningRecords::getDistance)
                        .sum();
                
                dailyReport.setRunCount(runCount);
                dailyReport.setDuration(totalDuration);
                dailyReport.setDistance(totalDistance);
                
                // 计算当天平均配速
                dailyReport.setPace(calculatePace(totalDuration, totalDistance));
            }
            
            dailyReports.add(dailyReport);
        }
        
        reportResp.setTrackList(dailyReports);
        return reportResp;
    }

    /**
     * 构建年报表
     */
    private ReportResp buildAnnualReport(Long accountId, Long objId, ReportResp reportResp) {
        // 根据objId从 watch_annual_running_stats 表中查出本年的统计数据
        AnnualRunningStats annualStats = annualRunningStatsMapper.selectById(objId);
        if (annualStats == null) {
            return reportResp;
        }

        // 设置基本统计信息
        reportResp.setRunCount(annualStats.getRunCount());
        reportResp.setDuration(annualStats.getTotalDuration());
        reportResp.setDistance(annualStats.getTotalDistance());
        
        // 计算平均配速
        reportResp.setPace(calculatePace(annualStats.getTotalDuration(), annualStats.getTotalDistance()));

        // 计算开始时间和结束时间
        String yearStr = annualStats.getYearStr();
        LocalDate yearStart = LocalDate.parse(yearStr + "-01-01");
        LocalDateTime yearStartTime = yearStart.atStartOfDay();
        LocalDateTime yearEndTime = yearStart.plusYears(1).atStartOfDay();

        // 一次性查询一年的所有月跑步统计数据
        String beginMonth = yearStr + "-01";
        String endMonth = yearStr + "-12";
        LambdaQueryWrapper<MonthlyRunningStats> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MonthlyRunningStats::getAccountId, accountId)
                .ge(MonthlyRunningStats::getYearMonthStr, beginMonth)
                .le(MonthlyRunningStats::getYearMonthStr, endMonth);
        
        List<MonthlyRunningStats> allYearlyMonthlyStats = monthlyRunningStatsMapper.selectList(queryWrapper);
        
        // 按年月字符串分组记录
        Map<String, MonthlyRunningStats> statsByMonth = allYearlyMonthlyStats.stream()
                .collect(Collectors.toMap(MonthlyRunningStats::getYearMonthStr, Function.identity()));

        // 查询每月的跑步记录
        List<TrackResp> monthlyReports = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            String monthStr = String.format("%s-%02d", yearStr, i + 1);
            MonthlyRunningStats monthlyStats = statsByMonth.get(monthStr);
            
            // 统计当月数据
            TrackResp monthlyReport = new TrackResp();
            if (monthlyStats != null) {
                monthlyReport.setRunCount(monthlyStats.getRunCount());
                monthlyReport.setDuration(monthlyStats.getTotalDuration());
                monthlyReport.setDistance(monthlyStats.getTotalDistance());
                
                // 计算当月平均配速
                monthlyReport.setPace(calculatePace(monthlyStats.getTotalDuration(), monthlyStats.getTotalDistance()));
            }
            
            monthlyReports.add(monthlyReport);
        }
        
        reportResp.setTrackList(monthlyReports);
        return reportResp;
    }

    /**
     * 按周、月、年统计跑步次数、总时间、总距离
     * 1. 周：从 watch_running_records 表中查询本周的数据并计算后写入到 watch_weekly_running_stats 表中，
     * 查询watch_weekly_running_stats时从上周的max_id 开始查询，避免全表扫描
     * 2. 月：从 watch_running_records 表中查询本月的数据并计算后写入到 watch_monthly_running_stats 表中，
     * 查询watch_monthly_running_stats时从上月的max_id 开始查询，避免全表扫描
     * 3. 年：从 watch_monthly_running_stats 表中查询本年数据并计算后写入到 watch_annual_running_stats 表中
     */
    public void calculateWeeklyMonthlyAnnualRunningStats() {
        // 1. 计算每周跑步统计数据
        calculateWeeklyRunningStats();

        // 2. 计算每月跑步统计数据
        calculateMonthlyRunningStats();

        // 3. 计每年跑步统计数据
        calculateAnnualRunningStats();
    }

    /**
     * 计算每周跑步统计数据
     */
    private void calculateWeeklyRunningStats() {
        LocalDate now = LocalDate.now().minusDays(1);
        String yearWeek = DateUtils.getYearWeekString(now);
        // 获取上周统计的max_id，避免全表扫描
        Long lastMaxId = getLastMaxId(now, "2");
        // 获取本周开始时间
        LocalDateTime thisWeekStart = now.minusDays(now.getDayOfWeek().getValue() - 1).atStartOfDay();
        LocalDateTime thisWeekEnd = thisWeekStart.plusDays(7);
        // 查询从lastMaxId开始的跑步记录
        LambdaQueryWrapper<RunningRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(RunningRecords::getId, RunningRecords::getAccountId, RunningRecords::getStartTime, RunningRecords::getEndTime, RunningRecords::getDistance)
                .ge(RunningRecords::getStartTime, thisWeekStart)
                .lt(RunningRecords::getStartTime, thisWeekEnd)
                .gt(RunningRecords::getId, lastMaxId)
                .orderByAsc(RunningRecords::getId);

        List<RunningRecords> records = runningRecordsMapper.selectList(queryWrapper);
        if (records.isEmpty()) {
            return;
        }
        // 按账号ID和周分组统计
        Map<Long, List<RunningRecords>> groupedByAccountAndWeek = records.stream()
                .collect(Collectors.groupingBy(RunningRecords::getAccountId));

        // 处理每个分组
        for (Map.Entry<Long, List<RunningRecords>> entry : groupedByAccountAndWeek.entrySet()) {
            Long accountId = entry.getKey();

            List<RunningRecords> weeklyRecords = entry.getValue();

            // 计算统计数据
            int[] stats = calculateRunningStats(weeklyRecords);
            int runCount = stats[0];
            int totalDuration = stats[1];
            int totalDistance = stats[2];

            // 创建或更新周统计记录
            LambdaQueryWrapper<WeeklyRunningStats> weekQuery = new LambdaQueryWrapper<>();
            weekQuery.eq(WeeklyRunningStats::getAccountId, accountId)
                    .eq(WeeklyRunningStats::getYearWeek, yearWeek);

            WeeklyRunningStats existingStats = weeklyRunningStatsMapper.selectOne(weekQuery);

            WeeklyRunningStats weeklyStats = new WeeklyRunningStats();
            weeklyStats.setAccountId(accountId);
            weeklyStats.setYearWeek(yearWeek);
            weeklyStats.setRunCount(runCount);
            weeklyStats.setTotalDuration(totalDuration);
            weeklyStats.setTotalDistance(totalDistance);
            weeklyStats.setUpdatedAt(LocalDateTime.now());

            if (existingStats != null) {
                weeklyRunningStatsMapper.update(weeklyStats, weekQuery);
            } else {
                weeklyRunningStatsMapper.insert(weeklyStats);
            }
        }

        // 更新 watch_running_records_job 表
        RunningRecordsJob job = runningRecordsJobMapper.selectOne(new LambdaQueryWrapper<RunningRecordsJob>()
                .eq(RunningRecordsJob::getYearStr, yearWeek));
        if (job == null) {
            job = new RunningRecordsJob();
            job.setYearStr("2" + yearWeek);
        }
        job.setMaxId(records.getLast().getId());
        runningRecordsJobMapper.insertOrUpdate(job);
    }

    /**
     * 计算每月跑步统计数据
     */
    private void calculateMonthlyRunningStats() {
        LocalDate now = LocalDate.now().minusDays(1);
        String yearMonth = DateUtils.getYearMonthString(now);
        // 获取上周统计的max_id，避免全表扫描
        Long lastMaxId = getLastMaxId(now, "1");
        // 本月开始时间
        LocalDateTime monthStart = now.withDayOfMonth(1).atStartOfDay();
        LocalDateTime monthEnd = monthStart.plusMonths(1);
        LambdaQueryWrapper<RunningRecords> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(RunningRecords::getId, RunningRecords::getAccountId, RunningRecords::getStartTime, RunningRecords::getEndTime, RunningRecords::getDistance)
                .ge(RunningRecords::getStartTime, monthStart)
                .lt(RunningRecords::getStartTime, monthEnd)
                .gt(RunningRecords::getId, lastMaxId)
                .orderByAsc(RunningRecords::getId);
        List<RunningRecords> records = runningRecordsMapper.selectList(queryWrapper);
        if (records.isEmpty()) {
            return;
        }

        // 按账号ID和月分组统计
        Map<Long, List<RunningRecords>> groupedByAccountAndMonth = records.stream()
                .collect(Collectors.groupingBy(RunningRecords::getAccountId));

        // 处理每个分组
        for (Map.Entry<Long, List<RunningRecords>> entry : groupedByAccountAndMonth.entrySet()) {
            Long accountId = entry.getKey();

            List<RunningRecords> monthlyRecords = entry.getValue();

            // 计算统计数据
            int[] stats = calculateRunningStats(monthlyRecords);
            int runCount = stats[0];
            int totalDuration = stats[1];
            int totalDistance = stats[2];

            // 创建或更新月统计记录
            LambdaQueryWrapper<MonthlyRunningStats> monthQuery = new LambdaQueryWrapper<>();
            monthQuery.eq(MonthlyRunningStats::getAccountId, accountId)
                    .eq(MonthlyRunningStats::getYearMonthStr, yearMonth);

            MonthlyRunningStats existingStats = monthlyRunningStatsMapper.selectOne(monthQuery);

            MonthlyRunningStats monthlyStats = new MonthlyRunningStats();
            monthlyStats.setAccountId(accountId);
            monthlyStats.setYearMonthStr(yearMonth);
            monthlyStats.setRunCount(runCount);
            monthlyStats.setTotalDuration(totalDuration);
            monthlyStats.setTotalDistance(totalDistance);

            if (existingStats != null) {
                monthlyRunningStatsMapper.update(monthlyStats, monthQuery);
            } else {
                monthlyRunningStatsMapper.insert(monthlyStats);
            }
        }
        // 更新 watch_running_records_job 表
        RunningRecordsJob job = runningRecordsJobMapper.selectOne(new LambdaQueryWrapper<RunningRecordsJob>()
                .eq(RunningRecordsJob::getYearStr, "1" + yearMonth));
        if (job == null) {
            job = new RunningRecordsJob();
            job.setYearStr("1" + yearMonth);
        }
        job.setMaxId(records.getLast().getId());
        runningRecordsJobMapper.insertOrUpdate(job);
    }

    /**
     * 计算每年跑步统计数据
     */
    private void calculateAnnualRunningStats() {
        LocalDate now = LocalDate.now().minusDays(1);
        // 获取年份
        String yearStr = String.valueOf(now.getYear());
        String beginMonth = yearStr + "-01";
        String endMonth = yearStr + "-12";
        // 查询所有月统计记录
        List<MonthlyRunningStats> monthlyStatsList = monthlyRunningStatsMapper.selectList(new LambdaQueryWrapper<MonthlyRunningStats>()
                .ge(MonthlyRunningStats::getYearMonthStr, beginMonth)
                .le(MonthlyRunningStats::getYearMonthStr, endMonth));

        // 按账号ID和年份分组统计
        Map<Long, List<MonthlyRunningStats>> groupedByAccountAndYear = monthlyStatsList.stream()
                .collect(Collectors.groupingBy(MonthlyRunningStats::getAccountId));

        // 处理每个分组
        for (Map.Entry<Long, List<MonthlyRunningStats>> entry : groupedByAccountAndYear.entrySet()) {
            Long accountId = entry.getKey();

            List<MonthlyRunningStats> yearlyStatsList = entry.getValue();

            // 计算统计数据
            int runCount = yearlyStatsList.stream()
                    .mapToInt(MonthlyRunningStats::getRunCount)
                    .sum();
            int totalDuration = yearlyStatsList.stream()
                    .mapToInt(MonthlyRunningStats::getTotalDuration)
                    .sum();
            int totalDistance = yearlyStatsList.stream()
                    .mapToInt(MonthlyRunningStats::getTotalDistance)
                    .sum();

            // 创建或更新年统计记录
            LambdaQueryWrapper<AnnualRunningStats> yearQuery = new LambdaQueryWrapper<>();
            yearQuery.eq(AnnualRunningStats::getAccountId, accountId)
                    .eq(AnnualRunningStats::getYearStr, yearStr);

            AnnualRunningStats existingStats = annualRunningStatsMapper.selectOne(yearQuery);

            AnnualRunningStats annualStats = new AnnualRunningStats();
            annualStats.setAccountId(accountId);
            annualStats.setYearStr(yearStr);
            annualStats.setRunCount(runCount);
            annualStats.setTotalDuration(totalDuration);
            annualStats.setTotalDistance(totalDistance);

            if (existingStats != null) {
                annualRunningStatsMapper.update(annualStats, yearQuery);
            } else {
                annualRunningStatsMapper.insert(annualStats);
            }
        }
    }

    /**
     * 获取上周统计的max_id
     */
    private Long getLastMaxId(LocalDate now, String suffix) {
        String yearWeekString = DateUtils.getYearWeekString(now.plusDays(7));
        WeeklyRunningStats lastWeeklyStats = weeklyRunningStatsMapper.selectOne(new LambdaQueryWrapper<WeeklyRunningStats>()
                .eq(WeeklyRunningStats::getYearWeek, suffix + yearWeekString));
        if (lastWeeklyStats != null) {
            return lastWeeklyStats.getId();
        }
        return 0L;
    }

}
