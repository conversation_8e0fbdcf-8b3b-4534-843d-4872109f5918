package com.fuyingedu.watch.model.running;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 保存跑步记录请求参数
 */
@Getter
@Setter
public class SaveReq {

    @NotNull(message = "账号ID不能为空")
    private Long accountId;
    /**
     * 跑步开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime startTime;

    /**
     * 跑步截止时间
     */
    @NotNull(message = "结束时间不能为空")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime endTime;

    /**
     * 跑步距离（单位：米）
     */
    @NotNull(message = "跑步距离不能为空")
    @Min(value = 0, message = "跑步距离不能为负数")
    private Integer distance;

    /**
     * 动态消耗卡路里
     */
    @NotNull(message = "动态消耗卡路里不能为空")
    @Min(value = 0, message = "动态消耗卡路里不能为负数")
    private Integer dynamicCalories;

    /**
     * 总消耗卡路里
     */
    @NotNull(message = "总消耗卡路里不能为空")
    @Min(value = 0, message = "总消耗卡路里不能为负数")
    private Integer totalCalories;

    /**
     * 平均功率（单位：瓦）
     */
    @Min(value = 0, message = "平均功率不能为负数")
    private Integer avgPower;

    /**
     * 平均步频（步/分钟）
     */
    @Min(value = 0, message = "平均步频不能为负数")
    private Short avgCadence;

    /**
     * 平均心率（bpm）
     */
    @Min(value = 0, message = "平均心率不能为负数")
    private Short avgHeartRate;

    @NotEmpty(message = "轨迹不能为空")
    @Valid
    private List<Track> trackList;

    @Getter
    @Setter
    public static class Track {

        /**
         * 纬度
         */
        @NotNull(message = "纬度不能为空")
        @DecimalMin(value = "-90", message = "纬度不能小于-90")
        @DecimalMax(value = "90", message = "纬度不能大于90")
        private BigDecimal latitude;

        /**
         * 经度
         */
        @NotNull(message = "经度不能为空")
        @DecimalMin(value = "-180", message = "经度不能小于-180")
        @DecimalMax(value = "180", message = "经度不能大于180")
        private BigDecimal longitude;

        /**
         * 数据时间
         */
        @NotNull(message = "数据时间不能为空")
        private LocalDateTime pointTime;
    }
}