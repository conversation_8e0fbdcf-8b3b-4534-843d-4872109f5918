package com.fuyingedu.watch.model.running;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class StatResp {

    /**
     * 统计对象ID，如周统计时为 watch_weekly_running_stats 表的主键ID
     */
    private Long objId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 跑步次数
     */
    private Integer runCount;

    /**
     * 总跑步时间（秒）
     */
    private Integer totalDuration;

    /**
     * 总跑步距离（米）
     */
    private Integer totalDistance;

    /**
     * 平均配速 （指每公里所花费的分钟数）
     */
    private String pace;

    /**
     * 统计时间 周（本周、上周、X月Y日-X月Z日） 月（本月、X年Y月） 年（X年）
     */
    private String statTime;
}
