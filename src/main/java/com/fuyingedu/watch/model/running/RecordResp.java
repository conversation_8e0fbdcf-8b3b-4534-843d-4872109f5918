package com.fuyingedu.watch.model.running;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
public class RecordResp {
    /**
     * 跑步记录ID
     */
    private Long id;

    /**
     * 记录开始时间
     */
    private LocalDateTime startTime;

    /**
     * 距离（单位：米）
     */
    private Integer distance;

    /**
     * 跑步时长（单位：秒）
     */
    private Integer duration;

    /**
     * 平均配速 （指每公里所花费的分钟数）
     */
    private String pace;

    /**
     * 跑步时间（今天、昨天、X月Y日）
     */
    private String runningTime;
}
