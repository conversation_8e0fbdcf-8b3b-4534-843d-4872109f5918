package com.fuyingedu.watch.model.running;

import com.fuyingedu.watch.model.running.TrackResp;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ReportResp {

    /**
     * 运动次数
     */
    private Integer runCount;

    /**
     * 距离（单位：米）
     */
    private Integer distance;

    /**
     * 跑步时长（单位：秒）
     */
    private Integer duration;

    /**
     * 平均配速 （指每公里所花费的分钟数）
     */
    private String pace;

    /**
     * 步频 步/分钟
     */
    private Short cadence;

    private List<TrackResp> trackList;
}
