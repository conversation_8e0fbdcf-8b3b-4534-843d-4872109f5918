package com.fuyingedu.watch.model.running;

import com.fuyingedu.watch.entity.RunningRecords;
import com.fuyingedu.watch.entity.RunningTrack;

import java.util.List;
import java.util.stream.Collectors;

public class RunningConvertor {

    public static RunningRecords toRunningRecords(SaveReq req) {
        RunningRecords records = new RunningRecords();
        records.setAccountId(req.getAccountId());
        records.setStartTime(req.getStartTime());
        records.setEndTime(req.getEndTime());
        records.setDistance(req.getDistance());
        records.setDynamicCalories(req.getDynamicCalories());
        records.setTotalCalories(req.getTotalCalories());
        records.setAvgPower(req.getAvgPower());
        records.setAvgCadence(req.getAvgCadence());
        records.setAvgHeartRate(req.getAvgHeartRate());
        return records;
    }
    
    public static List<RunningTrack> toRunningTrackList(List<SaveReq.Track> dtoList) {
        return dtoList.stream().map(dto -> {
            RunningTrack track = new RunningTrack();
            track.setLatitude(dto.getLatitude());
            track.setLongitude(dto.getLongitude());
            track.setPointTime(dto.getPointTime());
            return track;
        }).collect(Collectors.toList());
    }

    public static RecordResp toRecordResp(RunningRecords runningRecords) {
        RecordResp recordResp = new RecordResp();
        recordResp.setId(runningRecords.getId());
        recordResp.setStartTime(runningRecords.getStartTime());
        
        // Calculate duration in seconds
        if (runningRecords.getStartTime() != null && runningRecords.getEndTime() != null) {
            long duration = java.time.Duration.between(runningRecords.getStartTime(), runningRecords.getEndTime()).getSeconds();
            recordResp.setDuration((int) duration);
            
            // Calculate pace (minutes per kilometer)
            recordResp.setPace(getPace((int) duration, runningRecords.getDistance()));

            // Set running time description
            if (runningRecords.getStartTime() != null) {
                java.time.LocalDate runDate = runningRecords.getStartTime().toLocalDate();
                java.time.LocalDate today = java.time.LocalDate.now();
                java.time.LocalDate yesterday = today.minusDays(1);
                
                if (runDate.equals(today)) {
                    recordResp.setRunningTime("今天");
                } else if (runDate.equals(yesterday)) {
                    recordResp.setRunningTime("昨天");
                } else {
                    recordResp.setRunningTime(String.format("%d月%d日", runDate.getMonthValue(), runDate.getDayOfMonth()));
                }
            }
        }
        
        recordResp.setDistance(runningRecords.getDistance());
        return recordResp;
    }

    public static String getPace(Integer duration, Integer distance) {
        if (duration == null || duration <= 0 || distance == null || distance <= 0) {
            return "-";
        }
        double paceValue = (duration / 60.0) / (distance / 1000.0);
        // Format pace as "MM'SS\"" (minutes and seconds)
        int minutes = (int) paceValue;
        int seconds = (int) ((paceValue - minutes) * 60);
        return String.format("%d'%02d\"", minutes, seconds);
    }
}