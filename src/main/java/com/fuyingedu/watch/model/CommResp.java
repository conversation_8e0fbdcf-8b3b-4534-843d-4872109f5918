package com.fuyingedu.watch.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class CommResp<T> {

    /**
     * 状态码
     */
    private final String code;
    /**
     * 状态码描述
     */
    private final String message;
    /**
     * 数据
     */
    private T data;

    public CommResp(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static CommResp<?> success() {
        return success(null);
    }

    public static <T> CommResp<T> success(T data) {
        return new CommResp<>(RespMeta.SUCCESS.getStatus(), RespMeta.SUCCESS.getMsg(), data);
    }

    public static <T> CommResp<T> success(String msg, T data) {
        return new CommResp<>(RespMeta.SUCCESS.getStatus(), msg, data);
    }

    public static <T> CommResp<T> warning(RespMeta meta) {
        return new CommResp<>(meta.getStatus(), meta.getMsg(), null);
    }

    public static <T> CommResp<T> warning(String msg) {
        return new CommResp<>(RespMeta.SERVER_ERROR.getStatus(), msg, null);
    }

    public static <T> CommResp<T> error(String msg) {
        return new CommResp<>(RespMeta.SERVER_ERROR.getStatus(), msg, null);
    }

    public static <T> CommResp<T> warning(String status, String msg) {
        return new CommResp<>(status, msg, null);
    }

    public boolean isSuccess() {
        return RespMeta.SUCCESS.getStatus().equals(this.code);
    }

    public boolean isFail() {
        return !isSuccess();
    }
}
